/**
 * New Timeline Service
 * Unified timeline generation and management service
 */

import type {
  TimelineDisplay,
  TimelineEntry,
  PreviousTimeline,
  TimelineStatistics,
  Grievance,
  GrievanceStatus,
  SLAStatus,
} from "@/types/timeline";
import {
  getCheckpointInfo,
  getSLAConfig,
  isTerminalStatus,
} from "@/config/timelineConfig";

export class TimelineService {
  /**
   * Generate complete timeline display from grievance data
   */
  generateTimeline(grievance: Grievance): TimelineDisplay {
    try {
      console.log(
        "🔄 TimelineService: Generating timeline for grievance:",
        grievance._id
      );
      console.log("📊 Input data:", {
        status: grievance.status,
        priority: grievance.priority,
        statusHistoryCount: grievance.statusHistory?.length || 0,
        previousTimelinesCount: grievance.previousTimelines?.length || 0,
        reopenCount: grievance.reopenCount || 0,
      });

      // 🔍 DEBUG: Log the actual statusHistory structure
      console.log("🔍 Raw statusHistory:", grievance.statusHistory);
      console.log("🔍 Raw lifecycleHistory:", grievance.lifecycleHistory);
      console.log("🔍 Raw previousTimelines:", grievance.previousTimelines);

      // Generate timeline entries
      const entries = this.generateTimelineEntries(grievance);

      // Calculate statistics
      const statistics = this.calculateStatistics(entries, grievance);

      // Determine timeline properties
      const isCompleted = isTerminalStatus(grievance.status);
      const isTerminal = [
        "resolved",
        "closed",
        "rejected",
        "cancelled",
      ].includes(grievance.status);

      const timeline: TimelineDisplay = {
        id: `timeline-${grievance._id}-${grievance.reopenCount || 0}`,
        entries,
        startDate: grievance.submittedAt,
        endDate: isCompleted ? grievance.lastUpdatedAt : undefined,
        finalStatus: grievance.status,
        cycleNumber: (grievance.reopenCount || 0) + 1,
        isCompleted,
        isTerminal,
        duration: this.calculateDuration(
          grievance.submittedAt,
          grievance.lastUpdatedAt
        ),
        statistics,
      };

      console.log("✅ TimelineService: Timeline generated:", {
        entriesCount: entries.length,
        completedSteps: statistics.completedSteps,
        totalSteps: statistics.totalSteps,
        progress: `${(
          (statistics.completedSteps / statistics.totalSteps) *
          100
        ).toFixed(1)}%`,
      });

      return timeline;
    } catch (error) {
      console.error(
        "❌ TimelineService: Critical error in generateTimeline:",
        error
      );
      console.error("❌ Grievance data that caused the error:", grievance);

      // Return a safe fallback timeline
      return {
        id: `timeline-${grievance._id}-error`,
        entries: [],
        startDate: grievance.submittedAt || new Date().toISOString(),
        endDate: undefined,
        finalStatus: grievance.status || "unknown",
        cycleNumber: 1,
        isCompleted: false,
        isTerminal: false,
        duration: 0,
        statistics: {
          totalSteps: 0,
          completedSteps: 0,
          averageStepDuration: 0,
          totalDuration: 0,
          slaCompliance: {
            status: "ON_TRACK" as const,
            progress: 0,
            timeRemaining: 0,
            isOverdue: false,
          },
        },
      };
    }
  }

  /**
   * Generate timeline entries from grievance data
   */
  private generateTimelineEntries(grievance: Grievance): TimelineEntry[] {
    const entries: TimelineEntry[] = [];

    // 🚨 CRITICAL FIX: Ensure statusHistory is always a valid array
    let statusHistory = grievance.statusHistory || [];
    if (!Array.isArray(statusHistory)) {
      console.error(
        "❌ TimelineService: statusHistory is not an array, converting:",
        statusHistory
      );
      statusHistory = [];
    }

    // 🚨 CRITICAL FIX: Filter out null/undefined entries
    statusHistory = statusHistory.filter(
      (entry) => entry != null && typeof entry === "object"
    );

    const currentStatus = grievance.status;

    // 🔍 DEBUG: Log what we're working with
    console.log("🔍 generateTimelineEntries called with:", {
      originalStatusHistory: grievance.statusHistory,
      filteredStatusHistory: statusHistory,
      statusHistoryLength: statusHistory.length,
      currentStatus,
      grievanceId: grievance._id,
    });

    // Define the normal workflow sequence
    const workflowSequence: GrievanceStatus[] = [
      "submitted",
      "pending",
      "desk_1",
      "desk_2",
      "desk_3",
      "officer",
      "in_progress",
      "resolved",
      "closed",
    ];

    // Create entries based on status history
    statusHistory.forEach((historyEntry, index) => {
      const entry = this.createTimelineEntry(
        historyEntry,
        index + 1,
        grievance,
        index === statusHistory.length - 1 // is current step
      );
      entries.push(entry);
    });

    // If no status history, create entry for current status
    if (statusHistory.length === 0) {
      const syntheticHistory = {
        status: currentStatus,
        changedBy: "system",
        changedAt: grievance.submittedAt,
        reason: "Initial status",
      };

      const entry = this.createTimelineEntry(
        syntheticHistory,
        1,
        grievance,
        true
      );
      entries.push(entry);
    }

    // Add pending workflow steps for visualization
    const lastHistoryStatus =
      statusHistory.length > 0
        ? statusHistory[statusHistory.length - 1].status
        : currentStatus;

    const currentIndex = workflowSequence.indexOf(
      lastHistoryStatus as GrievanceStatus
    );

    if (currentIndex >= 0 && currentIndex < workflowSequence.length - 1) {
      // Add remaining workflow steps as pending
      for (let i = currentIndex + 1; i < workflowSequence.length; i++) {
        const futureStatus = workflowSequence[i];
        const futureEntry = this.createPendingTimelineEntry(
          futureStatus,
          entries.length + 1,
          grievance
        );
        entries.push(futureEntry);
      }
    }

    return entries;
  }

  /**
   * Create timeline entry from status history
   */
  private createTimelineEntry(
    historyEntry: any,
    stepNumber: number,
    grievance: Grievance,
    isCurrentStep: boolean
  ): TimelineEntry {
    const checkpointInfo = getCheckpointInfo(
      historyEntry.status as GrievanceStatus
    );
    const slaStatus = this.calculateSLAStatus(
      historyEntry.changedAt,
      grievance.priority
    );

    return {
      id: `entry-${grievance._id}-${stepNumber}`,
      status: historyEntry.status as GrievanceStatus,
      timestamp: historyEntry.changedAt,
      reason: historyEntry.reason,
      notes: historyEntry.notes,
      triggeredBy: {
        id: historyEntry.userId || "system",
        name: this.getUserName(historyEntry.changedBy),
        role: this.getUserRole(historyEntry.changedBy),
      },
      metadata: {
        stepNumber,
        hasBeenReached: true,
        isCurrentStep,
        duration: this.calculateStepDuration(
          historyEntry,
          grievance.statusHistory
        ),
        slaStatus,
        checkpointInfo,
      },
    };
  }

  /**
   * Create pending timeline entry for future steps
   */
  private createPendingTimelineEntry(
    status: GrievanceStatus,
    stepNumber: number,
    grievance: Grievance
  ): TimelineEntry {
    const checkpointInfo = getCheckpointInfo(status);

    return {
      id: `pending-${grievance._id}-${stepNumber}`,
      status,
      timestamp: "", // No timestamp for pending steps
      triggeredBy: {
        id: "",
        name: "Pending",
        role: "user",
      },
      metadata: {
        stepNumber,
        hasBeenReached: false,
        isCurrentStep: false,
        slaStatus: "ON_TRACK",
        checkpointInfo,
      },
    };
  }

  /**
   * Calculate timeline statistics
   */
  private calculateStatistics(
    entries: TimelineEntry[],
    grievance: Grievance
  ): TimelineStatistics {
    const completedSteps = entries.filter(
      (entry) => entry.metadata.hasBeenReached
    ).length;
    const totalSteps = entries.length;

    // Calculate average step duration
    const completedEntries = entries.filter(
      (entry) => entry.metadata.hasBeenReached && entry.metadata.duration
    );
    const totalDuration = completedEntries.reduce(
      (sum, entry) => sum + (entry.metadata.duration || 0),
      0
    );
    const averageStepDuration =
      completedEntries.length > 0 ? totalDuration / completedEntries.length : 0;

    // Calculate SLA compliance
    const slaCompliance = this.calculateSLACompliance(grievance);

    return {
      totalSteps,
      completedSteps,
      averageStepDuration,
      totalDuration,
      slaCompliance,
    };
  }

  /**
   * Calculate SLA compliance status
   */
  private calculateSLACompliance(grievance: Grievance) {
    try {
      const submittedDate = new Date(grievance.submittedAt);
      const currentDate = new Date();
      const slaConfig = getSLAConfig(grievance.priority);

      // CRITICAL FIX: Handle undefined slaConfig
      if (!slaConfig || !slaConfig.totalHours) {
        console.warn(
          "⚠️ TimelineService: Invalid SLA config for priority:",
          grievance.priority
        );
        return {
          status: "ON_TRACK" as SLAStatus,
          progress: 0,
          timeRemaining: 72 * 60 * 60 * 1000, // Default 72 hours
          isOverdue: false,
        };
      }

      const totalSlaTime = slaConfig.totalHours * 60 * 60 * 1000; // Convert to milliseconds
      const timeElapsed = currentDate.getTime() - submittedDate.getTime();
      const timeRemaining = totalSlaTime - timeElapsed;
      const isOverdue = timeRemaining < 0;

      // Calculate progress percentage
      const progress = Math.min((timeElapsed / totalSlaTime) * 100, 100);

      // Determine SLA status
      let status: SLAStatus;
      if (progress >= 100) status = "BREACHED";
      else if (progress >= 85) status = "CRITICAL";
      else if (progress >= 70) status = "URGENT";
      else status = "ON_TRACK";

      return {
        status,
        progress,
        timeRemaining: Math.max(timeRemaining, 0),
        isOverdue,
      };
    } catch (error) {
      console.error(
        "❌ TimelineService: Error calculating SLA compliance:",
        error
      );
      return {
        status: "ON_TRACK" as SLAStatus,
        progress: 0,
        timeRemaining: 72 * 60 * 60 * 1000, // Default 72 hours
        isOverdue: false,
      };
    }
  }

  /**
   * Calculate SLA status for specific timestamp
   */
  private calculateSLAStatus(timestamp: string, priority: string): SLAStatus {
    try {
      const date = new Date(timestamp);
      const currentDate = new Date();
      const slaConfig = getSLAConfig(priority as any);

      // CRITICAL FIX: Handle undefined slaConfig
      if (!slaConfig || !slaConfig.totalHours) {
        console.warn(
          "⚠️ TimelineService: Invalid SLA config for priority:",
          priority
        );
        return "ON_TRACK"; // Default fallback
      }

      const totalSlaTime = slaConfig.totalHours * 60 * 60 * 1000;
      const timeElapsed = currentDate.getTime() - date.getTime();
      const progress = (timeElapsed / totalSlaTime) * 100;

      if (progress >= 100) return "BREACHED";
      if (progress >= 85) return "CRITICAL";
      if (progress >= 70) return "URGENT";
      return "ON_TRACK";
    } catch (error) {
      console.error("❌ TimelineService: Error calculating SLA status:", error);
      return "ON_TRACK"; // Safe fallback
    }
  }

  /**
   * Calculate duration between two dates
   */
  private calculateDuration(startDate: string, endDate: string): string {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffMs = end.getTime() - start.getTime();

    const days = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const hours = Math.floor(
      (diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
    );
    const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

    if (days > 0) return `${days}d ${hours}h`;
    if (hours > 0) return `${hours}h ${minutes}m`;
    return `${minutes}m`;
  }

  /**
   * Calculate step duration
   */
  private calculateStepDuration(
    currentEntry: any,
    statusHistory: any[]
  ): number | undefined {
    // 🔍 DEBUG: Log inputs to identify the issue
    console.log("🔍 calculateStepDuration called with:", {
      currentEntry,
      statusHistory,
      statusHistoryType: typeof statusHistory,
      statusHistoryIsArray: Array.isArray(statusHistory),
    });

    // 🚨 CRITICAL FIX: Validate inputs
    if (!statusHistory || !Array.isArray(statusHistory)) {
      console.error(
        "❌ TimelineService: statusHistory is not a valid array:",
        statusHistory
      );
      return undefined;
    }

    if (!currentEntry) {
      console.error("❌ TimelineService: currentEntry is undefined");
      return undefined;
    }

    const currentIndex = statusHistory.findIndex(
      (entry) => entry === currentEntry
    );
    if (currentIndex <= 0) return undefined;

    const previousEntry = statusHistory[currentIndex - 1];
    const currentTime = new Date(currentEntry.changedAt).getTime();
    const previousTime = new Date(previousEntry.changedAt).getTime();

    return Math.floor((currentTime - previousTime) / (1000 * 60)); // Duration in minutes
  }

  /**
   * Get user name from identifier
   */
  private getUserName(identifier: string): string {
    // This would typically fetch from user service
    // For now, return the identifier or a formatted version
    if (identifier === "system") return "System";
    return identifier || "Unknown User";
  }

  /**
   * Get user role from identifier
   */
  private getUserRole(identifier: string): any {
    // This would typically fetch from user service
    // For now, return a default role
    if (identifier === "system") return "admin";
    return "user";
  }

  /**
   * Generate previous timelines from grievance data
   */
  generatePreviousTimelines(grievance: Grievance): PreviousTimeline[] {
    const previousTimelines: PreviousTimeline[] = [];

    if (grievance.previousTimelines && grievance.previousTimelines.length > 0) {
      grievance.previousTimelines.forEach((timeline, index) => {
        const previousTimeline: PreviousTimeline = {
          id: `prev-timeline-${grievance._id}-${index}`,
          cycleNumber: index + 1,
          startDate: timeline.startDate || grievance.submittedAt,
          endDate: timeline.endDate || timeline.completedAt || "",
          finalStatus: timeline.finalStatus as GrievanceStatus,
          entries: this.generateEntriesFromHistory(
            timeline.statusHistory || []
          ),
          completionReason: timeline.completionReason,
          reopenReason: timeline.reopenReason,
          statistics: this.calculateStatisticsFromHistory(
            timeline.statusHistory || []
          ),
        };

        previousTimelines.push(previousTimeline);
      });
    }

    return previousTimelines;
  }

  /**
   * Generate entries from status history
   */
  private generateEntriesFromHistory(statusHistory: any[]): TimelineEntry[] {
    return statusHistory.map((historyEntry, index) => ({
      id: `hist-entry-${index}`,
      status: historyEntry.status as GrievanceStatus,
      timestamp: historyEntry.changedAt,
      reason: historyEntry.reason,
      triggeredBy: {
        id: historyEntry.userId || "system",
        name: this.getUserName(historyEntry.changedBy),
        role: this.getUserRole(historyEntry.changedBy),
      },
      metadata: {
        stepNumber: index + 1,
        hasBeenReached: true,
        isCurrentStep: false,
        slaStatus: "ON_TRACK",
        checkpointInfo: getCheckpointInfo(
          historyEntry.status as GrievanceStatus
        ),
      },
    }));
  }

  /**
   * Calculate statistics from status history
   */
  private calculateStatisticsFromHistory(
    statusHistory: any[]
  ): TimelineStatistics {
    return {
      totalSteps: statusHistory.length,
      completedSteps: statusHistory.length,
      averageStepDuration: 0, // Would need to calculate from timestamps
      totalDuration: 0, // Would need to calculate from start/end dates
      slaCompliance: {
        status: "ON_TRACK",
        progress: 100,
        timeRemaining: 0,
        isOverdue: false,
      },
    };
  }
}
